import { Link } from '@/components/i18n/link';
import { useManagedWallet } from '@/hooks/header';
import { useAccount } from '@/hooks/wallet/account';
import { bitlayerMainnet } from '@/wallets/config/chains';
import { FormSection } from './_index';
import { COIN_NAME_TYPE } from '@/components/icons/coins';
import { Button } from '@/components/ui/button';
import ARBIcon from '@/components/icons/coins/ARBIcon';
import USDTIcon from '@/components/icons/coins/USDTIcon';
import USDCIcon from '@/components/icons/coins/USDCIcon';
import BNBIcon from '@/components/icons/coins/BNBIcon';
import ETHIcon from '@/components/icons/coins/ETHIcon';
import TRXIcon from '@/components/icons/coins/TRXIcon';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@react-hook/media-query';

import {
  useLocation,
  useFetcher,
  useLoaderData,
  useNavigate,
  useRevalidator,
} from '@remix-run/react';
import { CopyButton } from '@/components/featured/transaction';
import { useEffect, useState } from 'react';
import { intervalToDuration } from 'date-fns';
import CornerMark, { CornerMarkGroup } from '@/components/ui/corner-mark';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { QrCode } from '@/components/ui/qrcode.client';
import { ClientOnly } from 'remix-utils/client-only';
import { ArrowLeftIcon } from 'lucide-react';
import { ActionFunctionArgs, LoaderFunctionArgs, json } from '@remix-run/cloudflare';
import { useDialog } from '@/hooks/dialog';
import { createAPI } from '@/lib/api/gas-v2';
import { getSession } from '@/modules/session';
import { useTranslation } from 'react-i18next';

const i18Keys = 'pages.getGas';

interface PaymentData {
  asset: string;
  qrcode: string;
  address: string;
  chain?: string;
}

const optionMap: Record<string, AssetOption> = {
  USDT_ETH: {
    icon: USDTIcon,
    chainIcon: ETHIcon,
    value: 'USDT_ETH',
    label: 'USDT_Ethereum',
    chainName: 'Ethereum',
  },
  USDT_SEPOLIA: {
    icon: USDTIcon,
    chainIcon: ETHIcon,
    value: 'USDT_SEPOLIA',
    label: 'USDT Sepolia',
    chainName: 'Sepolia',
  },
  USDT_TRON: {
    icon: USDTIcon,
    chainIcon: TRXIcon,
    value: 'USDT_TRON',
    label: 'USDT_TRON',
    chainName: 'Tron',
  },
  USDT_BNB_TEST: {
    icon: USDTIcon,
    chainIcon: BNBIcon,
    value: 'USDT_BNB_TEST',
    label: 'USDT_BSC_TEST',
    chainName: 'BNB Smart Chain',
  },
  USDT_BNB: {
    icon: USDTIcon,
    chainIcon: BNBIcon,
    value: 'USDT_BNB',
    label: 'USDT_BSC',
    chainName: 'BNB Smart Chain',
  },
  USDC_ETH: {
    icon: USDCIcon,
    chainIcon: ETHIcon,
    value: 'USDC_ETH',
    label: 'USDC_Ethereum',
    chainName: 'Ethereum',
  },
  USDC_SEPOLIA: {
    icon: USDCIcon,
    chainIcon: ETHIcon,
    value: 'USDC_SEPOLIA',
    label: 'USDC Sepolia',
    chainName: 'Sepolia',
  },
  USDC_TRON: {
    icon: USDCIcon,
    chainIcon: TRXIcon,
    value: 'USDC_TRON',
    label: 'USDC_TRON',
    chainName: 'Tron',
  },
  USDC_BNB_TEST: {
    icon: USDCIcon,
    chainIcon: BNBIcon,
    value: 'USDC_BNB_TEST',
    label: 'USDC_BSC_TEST',
    chainName: 'BSC',
  },
  USDC_BNB: {
    icon: USDCIcon,
    chainIcon: BNBIcon,
    value: 'USDC_BNB',
    label: 'USDC_BSC',
    chainName: 'BSC',
  },
  USDT_ARBITRUM: {
    icon: USDTIcon,
    chainIcon: ARBIcon,
    value: 'USDT_ARBITRUM',
    label: 'USDT_Arbitrum',
    chainName: 'ARBITRUM',
  },
  USDC_ARBITRUM: {
    icon: USDCIcon,
    chainIcon: ARBIcon,
    value: 'USDC_ARBITRUM',
    label: 'USDC_Arbitrum',
    chainName: 'ARBITRUM',
  },
};

const WarningContent = ({ closeDialog }: { closeDialog: () => void }) => {
  const { t } = useTranslation();

  return (
    <div
      className="bl-text-center bl-pb-12 bl-items-center bl-p-6 bl-bg-contain"
      style={{
        background:
          'url(/images/gas/dialog-grid.png), linear-gradient(180deg, rgba(227, 110, 27, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%), #000',
      }}
    >
      <div className="bl-px-4 md:bl-px-[34px]">
        <div className="bl-text-2xl md:bl-text-[32px] bl-text-white bl-pt-5 bl-pb-3">
          {t(`${i18Keys}.right`)}
        </div>
        <div className="bl-text-2xl md:bl-text-3xl bl-text-[#FF0404] bl-font-extrabold bl-space-y-1">
          <div>{t(`${i18Keys}.Blockchain`)}</div>
          <div>{t(`${i18Keys}.Amount`)}</div>
          <div>{t(`${i18Keys}.token`)}</div>
        </div>
        <div className="md:bl-text-[20px] bl-text-base bl-pt-2 bl-pb-5 bl-text-white">
          {t(`${i18Keys}.otherwise`)}
          {t(`${i18Keys}.lose`)}
        </div>
        <div>
          <Button variant="outline-2" className="bl-w-[130px]" onClick={closeDialog}>
            <div>{t('common.Ok')}</div>
          </Button>
        </div>
      </div>
    </div>
  );
};

export async function loader({ params, context }: LoaderFunctionArgs) {
  try {
    const api = await createAPI(context);
    const orderNo = params.id as string;
    const resp = await api.getOrder(orderNo);

    const payments: PaymentData[] =
      resp.data.paymentInfos?.map((info) => ({
        asset: info.asset_id,
        qrcode: info.qrcode,
        address: info.to,
        chain: info.chain,
      })) || [];
    if (resp.data.status === 4) {
      resp.data.expireAt = 0;
    }

    const unit = payments[0]?.asset.split('_')[0] || 'USDT';

    const amounts = await api.estimateAmount({
      amount: [String(Number(resp.data.fromFaceValue) - 1)],
      from_coin: unit as COIN_NAME_TYPE,
      to_coin: 'BTC',
    });

    const isToBtc = resp.data.toAssetId?.startsWith('BTC');
    const btcValue = isToBtc ? amounts.data.face_values[0].target_face_value : undefined;

    return json({
      amount: resp.data.fromFaceValue,
      address: resp.data.to,
      payments,
      expireAt: resp.data.expireAt,
      orderNo,
      status: resp.data.status,
      btcValue,
    });
  } catch (e) {
    return json({
      amount: 'resp.data.fromFaceValue',
      address: ' resp.data.to',
      payments: [],
      expireAt: 'resp.data.expireAt',
      orderNo: 1,
      status: 'resp.data.status',
      btcValue: 'resp.data.btcValue',
    });
  }
}

export async function action({ params, request, context }: ActionFunctionArgs) {
  const session = await getSession(request);
  const orderNo = params.id as string;
  const api = await createAPI(context);

  const key = session.get('gas.encrypt_key') as string | undefined;
  const requestId = session.get('gas.request_id') as string | undefined;

  if (!key || !requestId) {
    throw new Response(null, { status: 400, statusText: 'Bad Request' });
  }

  try {
    await api.cancelOrder(orderNo, { key, requestId });
  } catch (e: unknown) {
    console.error(`Failed to cancel order ${orderNo}:`, e);
  }

  return null;
}

const PageMain = ({ children, address }: { address?: string; children: React.ReactNode }) => {
  const { open } = useDialog();
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [visible, setVisible] = useState(true);
  const location = useLocation();
  const search = location.search;
  const urlParams = new URLSearchParams(search);
  const source = urlParams.get('source');
  const sourceParam = source ? `?source=${source}` : '';

  const handleClickCancelOrder = () => {
    open({
      content: ({ close }) => {
        const handleCancel = async () => {
          close();
          fetcher.submit({ action: 'cancel' }, { method: 'POST' });
          navigate(`/flash-bridge${sourceParam}`);
        };

        return (
          <div className="bl-text-center bl-p-5">
            <p className="bl-text-primary-foreground bl-w-60">{t(`${i18Keys}.cancelWarning`)}</p>
            <div className="bl-flex bl-justify-center bl-gap-15 bl-pt-5">
              <Button variant="dark" className="bl-w-44" asChild onClick={handleCancel}>
                <Link to={`/flash-bridge${sourceParam}`}>
                  <span>{t(`${i18Keys}.ok`)}</span>
                </Link>
              </Button>
            </div>
          </div>
        );
      },
    });
  };

  const closeDialog = () => {
    setVisible(false);
  };

  return (
    <div className="bl-w-full md:bl-w-[600px] bl-mt-7.5 md:bl-mt-5 bl-space-y-3">
      <div className="bl-flex">
        <Button variant="outline" size="xs" asChild>
          <Link to={`/flash-bridge${sourceParam}`} className="bl-text-sm/none">
            <div className="bl-flex bl-items-center bl-gap-1">
              <ArrowLeftIcon className="bl-size-4" />
              <span>{t('common.back')}</span>
            </div>
          </Link>
        </Button>
      </div>
      <div className="bl-bg-card-background bl-space-y-2 bl-border bl-border-card-border bl-p-3 md:bl-p-7.5">
        {children}
      </div>
      <div className="bl-flex bl-justify-center bl-gap-6 md:bl-gap-15 bl-pt-5">
        <Button
          variant="outline"
          className="bl-w-[110px] md:bl-w-44"
          onClick={handleClickCancelOrder}
        >
          <span className="bl-text-sm md:bl-text-lg">{t(`${i18Keys}.cancel`)}</span>
        </Button>
        <Button
          variant="default"
          overlayVariant="outline"
          className="bl-w-[110px] md:bl-w-44"
          asChild
        >
          <Link to={`/flash-bridge/history${sourceParam}`} state={{ address }}>
            <span className="bl-text-sm md:bl-text-lg">{t(`${i18Keys}.Complete`)}</span>
          </Link>
        </Button>
      </div>
      <div className="bl-flex bl-flex-col bl-items-center bl-text-primary bl-pt-7">
        <h4 className="bl-text-sm">Note:</h4>
        <p className="bl-text-xs bl-w-72">{t(`${i18Keys}.getBitlayerGas`)}</p>
      </div>
      <Dialog open={visible}>
        <DialogContent className="md:bl-w-[460px] bl-p-0">
          <div className="bl-relative bl-z-20 bl-min-h-10 bl-font-body">
            <WarningContent closeDialog={closeDialog} />
            <CornerMarkGroup />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

interface AssetOption {
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  chainIcon: React.FC<React.SVGProps<SVGSVGElement>>;
  value: string;
  label: string;
  chainName?: string;
}

const AssetSelectItem = ({
  option,
  active,
  onSelect,
}: {
  option: AssetOption;
  active?: boolean;
  onSelect?: (value: string) => void;
}) => {
  const AssetIcon = option.icon;
  const ChainIcon = option.chainIcon;
  return (
    <Button
      variant={active ? 'default' : 'outline'}
      overlayVariant={active ? 'secondary' : 'outline'}
      className={cn('bl-text-lg/none bl-justify-start bl-w-full', {
        'bl-text-white': active,
      })}
      onClick={() => onSelect?.(option.value)}
    >
      <div className="bl-flex bl-items-center bl-gap-4 md:bl-px-6">
        <div className="bl-relative">
          <AssetIcon className="bl-size-7" />
          <ChainIcon className="bl-size-4 bl-absolute bl-bottom-0 -bl-right-2" />
        </div>
        <span className="bl-text-sm">{option.label}</span>
      </div>
    </Button>
  );
};

const AssetSelectionGroup = ({
  options,
  value,
  onChange,
}: {
  options: AssetOption[];
  value?: string;
  onChange?: (value: string) => void;
}) => {
  return (
    <div className="bl-grid md:bl-grid-cols-2 bl-items-center bl-justify-center bl-gap-2 md:bl-gap-4">
      {options.map((option, index) => (
        <AssetSelectItem
          key={index}
          option={option}
          active={value === option.value}
          onSelect={onChange}
        />
      ))}
    </div>
  );
};

const PaymentAddressSection = ({ address, chain }: { address: string; chain: string }) => {
  const { t } = useTranslation();

  return (
    <div className="bl-text-center bl-mt-7">
      <h4 className="bl-flex bl-text-center bl-justify-center">
        <span className="bl-block md:bl-hidden bl-text-base md:bl-text-[22px] bl-text-primary bl-font-[400]">
          {chain}&nbsp;
        </span>
        {t(`${i18Keys}.payment`)}
      </h4>
      <div className="bl-flex bl-gap-2 bl-items-center">
        <span className="bl-hidden md:bl-block bl-text-base bl-whitespace-nowrap bl-text-primary bl-font-[400]">
          {chain}:
        </span>
        <span className="bl-text-sm md:bl-text-base bl-text-white">{address}</span>
        <CopyButton text={address} className="bl-text-primary" />
      </div>
    </div>
  );
};

const CountDownSection = ({
  expireAt,
  onTimeout,
}: {
  expireAt: number;
  onTimeout?: () => void;
}) => {
  const [seconds, setSeconds] = useState(expireAt - new Date().getTime());
  useEffect(() => {
    const timer = setInterval(() => {
      if (seconds <= 1000) {
        onTimeout?.();
        clearInterval(timer);
      }
      setSeconds(expireAt - new Date().getTime());
    }, 1000);
    return () => clearInterval(timer);
  });

  const duration = intervalToDuration({ start: 0, end: seconds });
  const formatted = [duration.hours, duration.minutes, duration.seconds]
    .map((num = 0) => String(num).padStart(2, '0'))
    .join(':');
  return (
    <div
      className={cn('bl-text-primary bl-text-xl/none bl-mb-3', {
        'bl-invisible': seconds <= 0,
      })}
    >
      {formatted}
    </div>
  );
};

const QrCodeSection = ({
  address,
  qrcode,
  expireAt,
  isTimeout,
  onTimeout,
  asset,
}: {
  qrcode: string;
  address: string;
  expireAt: number;
  isTimeout?: boolean;
  asset: string;
  onTimeout?: () => void;
}) => {
  const assetInfo = optionMap[asset];

  const { t } = useTranslation();
  return (
    <div className="bl-flex bl-flex-col bl-items-center bl-p-5">
      <CountDownSection expireAt={expireAt} onTimeout={onTimeout} />
      <div className="bl-size-[264px] bl-relative">
        <QrCode value={qrcode} cover={`/images/gas/${asset}.png`} size={264} />
        {isTimeout && (
          <div
            className={cn(
              'bl-absolute -bl-left-3.5 -bl-top-3.5 bl-size-[300px]',
              'bl-flex bl-items-center bl-justify-center',
              'bl-border bl-border-input bl-bg-black/90 bl-bg-gradient-to-t bl-from-[#DE6B1A]/5 bl-to-[#DE6B1A]/20',
            )}
          >
            <div
              className={cn(
                'bl-px-5 bl-py-1 bl-flex bl-gap-2 bl-items-center bl-relative bl-bg-primary/30',
              )}
            >
              <div className="bl-text-primary bl-cursor-default">{t(`${i18Keys}.timeout`)}</div>
              <CornerMark position="tl" className="[--bl-space:0]" />
              <CornerMark position="br" className="[--bl-space:0]" />
            </div>
            <CornerMark position="tl" className="[--bl-space:8px]" />
            <CornerMark position="tr" className="[--bl-space:8px]" />
            <CornerMark position="bl" className="[--bl-space:8px]" />
            <CornerMark position="br" className="[--bl-space:8px]" />
          </div>
        )}
      </div>
      {isTimeout ? (
        <div className="bl-mt-7 bl-text-xs bl-text-button-error">{t(`${i18Keys}.warning`)}</div>
      ) : (
        <PaymentAddressSection address={address} chain={assetInfo.chainName as string} />
      )}
    </div>
  );
};
const feePromotion = 1;

export default function GasV2OrderPage() {
  const { payments, amount, address, expireAt, status, btcValue } = useLoaderData<typeof loader>();

  const navigate = useNavigate();
  const revalidator = useRevalidator();
  const { t, i18n } = useTranslation();
  const isJa = i18n.language === 'ja';
  const isVi = i18n.language === 'vi';
  const unit = payments[0]?.asset.split('_')[0] || 'USDT';
  const chain = bitlayerMainnet;
  useManagedWallet({ chain });
  const { address: walletAddress } = useAccount({ network: chain.networkType });

  const [isTimeout, setIsTimeout] = useState(expireAt < new Date().getTime());
  const [activePayment, setActivePayment] = useState<PaymentData>(payments[0]);

  const location = useLocation();
  const search = location.search;
  const urlParams = new URLSearchParams(search);
  const source = urlParams.get('source');
  const sourceParam = source ? `?source=${source}` : '';
  const isMobile = useMediaQuery('(max-width: 640px)');

  useEffect(() => {
    if (status === 2) {
      navigate(`../../history${sourceParam}`);
    }
  }, [status, navigate]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (status === 4 || isTimeout) {
        clearInterval(timer);
      }
      revalidator.revalidate();
    }, 5_000);
    return () => clearInterval(timer);
  });

  const options: AssetOption[] = payments.map(
    (payment) => optionMap[payment.asset] || optionMap.USDT_ETH,
  );

  const handleSelectAsset = (value: string) => {
    const payment = payments.find((p) => p.asset === value);
    if (!payment) return;
    setActivePayment(payment);
  };
  const getAmount = Number(amount) - feePromotion;

  return (
    <PageMain address={walletAddress || address}>
      <FormSection className="md:bl-text-center">
        <div>
          {t(`${i18Keys}.amount`)}{' '}
          <span className="bl-text-white">
            {amount} {unit}
          </span>
          <div className="bl-text-xs">
            {t(`${i18Keys}.get`)}:
            {btcValue
              ? `${Number(btcValue) > 0 ? btcValue : 0}BTC（${getAmount > 0 ? getAmount : 0} ${unit}）`
              : `${getAmount > 0 ? getAmount : 0} ${unit}`}
          </div>
        </div>
      </FormSection>
      <FormSection className="md:bl-text-center">
        <div className="bl-flex bl-flex-col md:bl-flex-row md:bl-items-center bl-gap-1 ">
          <span>{t(`${i18Keys}.recipient`)}:</span>
          <span className="bl-text-white bl-text-sm bl-break-all">{address}</span>
        </div>
      </FormSection>

      <FormSection className="bl-text-center">
        <div
          className={cn('bl-px-6 md:bl-px-[50px] bl-flex bl-gap-6', {
            'bl-pl-0': (isJa || isVi) && isMobile,
          })}
        >
          <div className="bl-flex bl-items-center">
            <div>
              <div className="bl-text-white bl-text-sm md:bl-text-base">
                {t(`${i18Keys}.right`)}
              </div>
              <div className="bl-text-xs bl-whitespace-nowrap">
                <div className="bl-block md:bl-inline">{t(`${i18Keys}.otherwise`)}</div>
                {t(`${i18Keys}.lose`)}
              </div>
            </div>
          </div>
          <div className="bl-flex bl-flex-col bl-items-center bl-space-y-1 bl-text-lg md:bl-text-xl bl-w-full bl-text-[#FF0404] bl-font-[800]">
            <span>{t(`${i18Keys}.Blockchain`)}</span>
            <span className="bl-whitespace-nowrap">
              {t(`${i18Keys}.Amount`)} &nbsp; {t(`${i18Keys}.token`)}
            </span>
            <span className="bl-text-xs md:bl-text-sm bl-font-medium">{t(`${i18Keys}.pay`)}</span>
          </div>
        </div>
      </FormSection>

      <ClientOnly>
        {() => (
          <QrCodeSection
            expireAt={expireAt}
            address={activePayment.address}
            qrcode={activePayment.qrcode}
            chain={activePayment.chain as string}
            asset={activePayment.asset as string}
            isTimeout={isTimeout}
            onTimeout={() => setIsTimeout(true)}
          />
        )}
      </ClientOnly>
      {!isTimeout && (
        <AssetSelectionGroup
          options={options}
          value={activePayment.asset}
          onChange={handleSelectAsset}
        />
      )}
    </PageMain>
  );
}
