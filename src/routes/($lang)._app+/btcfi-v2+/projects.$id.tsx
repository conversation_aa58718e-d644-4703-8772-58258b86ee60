import { Card, CardContent, CutCornerCard } from '@/components/ui/card';
import { ProjectCard } from '@/modules/btcfi-v2/components/project';
import { Link, useLoaderData } from '@remix-run/react';
import { ChevronsLeftIcon } from 'lucide-react';
import { DepositForm } from '@/modules/btcfi-v2/components/deposit';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RedeemForm } from '@/modules/btcfi-v2/components/redeem';
import { APYChart } from '@/modules/btcfi-v2/components/apy-chart';
import { BtcfiV2CustodyToken, BtcfiV2Project } from '@/modules/btcfi/types';
import { ThirdPartyContent } from '@/modules/btcfi-v2/components/third-party';
import { json, LoaderFunctionArgs } from '@remix-run/cloudflare';
import { createAPIClient } from '@/modules/btcfi/api.server';
import { cn, toPercent } from '@/lib/utils';
import { Token } from '@/wallets/config/type';
import { chainMap } from '@/wallets/config/chains';
import { DynamicIcon } from '@/components/ui/icon';
import { AnimatePageLayout } from '@/components/ui/page';
import { useTranslation } from 'react-i18next';

export async function loader({ params }: LoaderFunctionArgs) {
  const { id } = params;
  const api = createAPIClient();

  const projectId = Number(id);
  const project = await api.getBtcfiV2ProjectInfo(projectId);
  const apyHistory = await api.getBtcfiV2ProjectAPYHistory(projectId);
  const relations = await api.getBtcfiV2ProjectRelations(projectId);

  return json({ project, apyHistory, relations });
}

export default function BTCFiProjectPage() {
  const { project, apyHistory, relations } = useLoaderData<typeof loader>();
  const { t } = useTranslation();

  return (
    <AnimatePageLayout>
      <section className="bl-container bl-mb-12 bl-pt-20 bl-pb-24 lg:bl-w-[1220px]">
        <div
          className={cn(
            'bl-w-full bl-pt-6 bl-h-[80px] bl-bg-contain bl-bg-no-repeat bl-bg-bottom lg:bl-h-[200px] lg:bl-pt-15 bl-relative',
            {
              'lg:bl-h-[145px]': !project.banner,
            },
          )}
          style={project.banner ? { backgroundImage: `url(${project.banner})` } : {}}
        >
          <Breadcrumb project={project} />
          {!project.banner && (
            <div className="bl-absolute bl-size-[378px] bl-bg-primary bl-rounded-full bl-opacity-80 bl-blur-[256px] bl-top-[-240px] bl-left-[80px]"></div>
          )}
        </div>

        <div className="bl-flex bl-flex-col lg:bl-flex-row bl-gap-6">
          <Card className="bl-grow bl-bg-transparent bl-text-secondary">
            {apyHistory.length > 0 && (
              <div className="bl-mb-6 -bl-translate-x-2 bl-translate-y-3 lg:-bl-translate-x-4 lg:bl-translate-y-9">
                <APYChart data={apyHistory} />
              </div>
            )}
            <CardContent className="bl-p-3 lg:bl-p-9">
              <div className="bl-border-b bl-border-card-border bl-py-8 bl-grid bl-grid-cols-2">
                <div className="bl-space-y-2">
                  <div className="bl-text-sm lg:bl-text-base">{t('pages.btcfi.tvl')}</div>
                  <div className="bl-text-white bl-text-xl bl-font-bold lg:bl-text-[22px]">
                    ${Number(project.tvl).toLocaleString('en-US')}
                  </div>
                </div>
                <div className="bl-space-y-2">
                  <div className="bl-text-sm lg:bl-text-base">{t('pages.btcfi.estAPY')}</div>
                  <div className="bl-text-white bl-text-xl bl-font-bold lg:bl-text-[22px]">
                    {project.apy === undefined ? '--' : toPercent(project.apy)}
                  </div>
                </div>
              </div>
              <div
                className="bl-border-b bl-border-card-border bl-py-8 btcfi-v2-article last:bl-border-0"
                dangerouslySetInnerHTML={{
                  __html: project.description,
                }}
              ></div>
              {relations && relations.length > 0 && (
                <div className="bl-py-8">
                  <h3 className="bl-text-sm lg:bl-text-base">
                    {t('pages.btcfi.tokenEarn', { name: project.name })}
                  </h3>
                  <div className="bl-w-full bl-grid bl-gap-3.5 bl-mt-8 lg:bl-grid-cols-2 lg:bl-gap-7">
                    {relations.map((project) => (
                      <ProjectCard key={project.id} project={project} className="bl-w-full" />
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <CutCornerCard
            containerClassName="bl-w-full bl-h-fit bl-order-first lg:bl-w-fit lg:bl-order-last"
            className="bl-w-full bl-py-10 bl-px-5 bl-bg-[#111] bl-space-y-8 lg:bl-w-[412px]"
          >
            <ActionCardContent project={project} />
          </CutCornerCard>
        </div>
      </section>
    </AnimatePageLayout>
  );
}

function Breadcrumb({ project }: { project: BtcfiV2Project }) {
  return (
    <Link className="bl-flex bl-items-center bl-gap-2 hover:bl-underline" to="/btcfi-v2">
      <ChevronsLeftIcon className="bl-size-6" />
      <div className="bl-flex bl-items-center bl-gap-1">
        <DynamicIcon icon={project.icon} className="bl-size-6" />
        <div>{project.name}</div>
      </div>
    </Link>
  );
}

type ActionCardContentProps = {
  project: BtcfiV2Project;
};

function ActionCardContent({ project }: ActionCardContentProps) {
  const { t } = useTranslation();
  const custody = project.custody;

  if (custody.self && custody.thirdParty) {
    const chain = chainMap[custody.self.staked[0].chain];
    const stakedToken = custody.self.staked.map(toToken);
    const receiptToken = toToken(custody.self.receipt);
    return (
      <Tabs defaultValue="self">
        <TabsList className="bl-w-full bl-grid bl-grid-cols-2 bl-h-9 bl-bg-secondary bl-p-0.5 bl-border-0">
          <TabsTrigger
            value="self"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            {t('pages.btcfi.custody.self')}
          </TabsTrigger>
          <TabsTrigger
            value="thirdParty"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            {t('pages.btcfi.custody.thirdParty')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="self" className="bl-mt-5">
          <Tabs defaultValue="deposit">
            <TabsList className="bl-w-full bl-grid bl-grid-cols-2 bl-h-9 bl-bg-transparent bl-p-0.5 bl-border-0">
              <TabsTrigger
                value="deposit"
                className="bl-w-full bl-text-lg bl-justify-start bl-font-medium bl-text-secondary/50 data-[state=active]:bl-bg-transparent data-[state=active]:bl-text-white"
              >
                {t('pages.btcfi.custody.deposit')}
              </TabsTrigger>
              <TabsTrigger
                value="redeem"
                className="bl-w-full bl-text-lg bl-justify-start bl-font-medium bl-text-secondary/50 data-[state=active]:bl-bg-transparent data-[state=active]:bl-text-white"
              >
                {t('pages.btcfi.custody.redeem')}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="deposit" className="bl-mt-2">
              <DepositForm
                sourceTokens={stakedToken}
                targetToken={receiptToken}
                chain={chain}
                contract={custody.self.contract}
              />
            </TabsContent>
            <TabsContent value="redeem" className="bl-mt-2">
              <RedeemForm
                sourceToken={receiptToken}
                targetTokens={stakedToken}
                chain={chain}
                contract={custody.self.contract}
              />
            </TabsContent>
          </Tabs>
        </TabsContent>
        <TabsContent value="thirdParty" className="bl-mt-5">
          <ThirdPartyContent project={project} custody={custody.thirdParty} name={project.name} />
        </TabsContent>
      </Tabs>
    );
  } else if (custody.self && !custody.thirdParty) {
    const chain = chainMap[custody.self.staked[0].chain];
    const stakedToken = custody.self.staked.map(toToken);
    const receiptToken = toToken(custody.self.receipt);
    return (
      <Tabs defaultValue="deposit">
        <TabsList className="bl-w-full bl-grid bl-grid-cols-2 bl-h-9 bl-bg-secondary bl-p-0.5 bl-border-0">
          <TabsTrigger
            value="deposit"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            {t('pages.btcfi.custody.deposit')}
          </TabsTrigger>
          <TabsTrigger
            value="redeem"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            {t('pages.btcfi.custody.redeem')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="deposit" className="bl-mt-5">
          <DepositForm
            sourceTokens={stakedToken}
            targetToken={receiptToken}
            chain={chain}
            contract={custody.self.contract}
          />
        </TabsContent>
        <TabsContent value="redeem" className="bl-mt-5">
          <RedeemForm
            sourceToken={receiptToken}
            targetTokens={stakedToken}
            chain={chain}
            contract={custody.self.contract}
          />
        </TabsContent>
      </Tabs>
    );
  } else if (custody.thirdParty && !custody.self) {
    return <ThirdPartyContent project={project} custody={custody.thirdParty} name={project.name} />;
  }

  return null;
}

function toToken(token: BtcfiV2CustodyToken): Token {
  if (token.type === 'native') {
    return {
      id: token.id,
      name: token.name,
      symbol: token.symbol,
      decimals: token.decimals,
      isNative: true,
      type: 'native',
      icon: token.icon,
    };
  }

  return {
    id: token.id,
    name: token.name,
    symbol: token.symbol,
    decimals: token.decimals,
    isNative: false,
    type: 'erc20',
    icon: token.icon,
    contractAddress: token.address,
  };
}
