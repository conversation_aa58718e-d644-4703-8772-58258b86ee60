import { Switch<PERSON><PERSON><PERSON><PERSON><PERSON>on, WalletConnector } from '@/components/featured/wallet';
import { Button } from '@/components/ui/button';
import { AmountInput } from '@/components/ui/form-field';
import { DynamicIcon } from '@/components/ui/icon';
import { useAccount } from '@/hooks/wallet/account';
import { useWalletBalance } from '@/hooks/wallet/balance';
import { BaseChainType, Token } from '@/wallets/config/type';
import { ArrowDownIcon, LoaderIcon } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Address, parseUnits } from 'viem';
import { useRedeem } from '../hooks/redeem';
import { useToast } from '@/hooks/toast';
import { ReadableError } from '@/modules/bridge/transactions/errors';
import { ButtonSelectTrigger, Select, SelectContent, SelectItem } from '@/components/ui/select';

export type RedeemFormProps = {
  chain: BaseChainType;
  sourceToken: Token;
  targetTokens: Token[];
  contract: Address;
};

export function RedeemForm({ sourceToken, targetTokens, chain, contract }: RedeemFormProps) {
  const { address, chain: currentChain } = useAccount({ network: chain.networkType });
  const { t } = useTranslation();
  const { toast } = useToast();
  const [amount, setAmount] = useState('');
  const [targetTokenIndex, setTargetTokenIndex] = useState(0);
  const targetToken = targetTokens[targetTokenIndex];

  const { data: balance, refetch: refetchSource } = useWalletBalance({
    network: chain.networkType,
    address,
    token: sourceToken,
    chain,
  });

  const { data: targetBalance, refetch: refetchTarget } = useWalletBalance({
    network: chain.networkType,
    address,
    token: targetToken,
    chain,
  });

  const { redeemAsync, isPending } = useRedeem();

  const amountValidator = (value: string) => {
    try {
      parseUnits(value, sourceToken.decimals);
    } catch (error) {
      return false;
    }

    const [, decimal] = value.split('.');
    if (decimal && decimal.length > sourceToken.decimals) {
      return false;
    }

    return !isNaN(Number(value)) && Number(value) >= 0;
  };

  const handleClickMax = () => {
    if (balance) {
      setAmount(balance.formatted);
    }
  };

  const [isAmountValid, amountTips] = useMemo(() => {
    if (!amount || !balance) {
      return [false, t('common.confirm')];
    }

    let bigAmount = 0n;
    try {
      bigAmount = parseUnits(amount, sourceToken.decimals);
    } catch (error) {
      return [false, t('pages.bridge.invalidAmount')];
    }

    if (bigAmount === 0n) {
      return [false, t('pages.bridge.invalidAmount')];
    }

    if (bigAmount > balance.value) {
      return [false, t('common.insufficientBalance')];
    }

    return [true, ''];
  }, [amount, balance, sourceToken.decimals, t]);

  const handleRedeem = async () => {
    if (!isAmountValid) {
      return;
    }

    try {
      await redeemAsync({
        address: address as Address,
        token: sourceToken,
        amount: parseUnits(amount, sourceToken.decimals),
        contract: contract,
        contractType: 'yBTC.B',
      });
      toast(t('pages.btcfi.custody.redeemSuccess'));
    } catch (e) {
      if (e instanceof ReadableError) {
        toast(t(`pages.bridge.errors.${e.message}`));
      } else {
        toast(t('pages.btcfi.custody.redeemFailed'));
      }
      return;
    }

    refetchSource();
    refetchTarget();
    setAmount('');
  };

  const renderActionButton = () => {
    if (!address || !currentChain) {
      return (
        <WalletConnector chain={chain}>
          <Button className="bl-w-full" overlayFrom="none">
            {t('common.connect')}
          </Button>
        </WalletConnector>
      );
    }

    if (currentChain.id !== chain.id) {
      return <SwitchChainButton chain={chain} className="bl-w-full" overlayFrom="none" />;
    }

    if (isPending) {
      return (
        <Button className="bl-w-full bl-gap-1" overlayFrom="none" disabled>
          <LoaderIcon className="bl-size-6 bl-animate-spin" />
          <span>{t('common.pending')}</span>
        </Button>
      );
    }

    if (!isAmountValid) {
      return (
        <Button className="bl-w-full" overlayFrom="none" disabled>
          <span>{amountTips}</span>
        </Button>
      );
    }

    return (
      <Button overlayFrom="none" className="bl-w-full" onClick={handleRedeem}>
        <span>{t('common.confirm')}</span>
      </Button>
    );
  };

  return (
    <div className="bl-space-y-3">
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black bl-relative">
        <div>{t('pages.btcfi.custody.redeem')}</div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <AmountInput
            value={amount}
            onChange={setAmount}
            validator={amountValidator}
            className="bl-w-30"
            placeholder="0.00"
          />
          <Button variant="outline-3" className="bl-w-32 disabled:bl-opacity-100" disabled>
            <div className="bl-flex bl-items-center bl-gap-2">
              <DynamicIcon icon={sourceToken.icon} className="bl-size-5" />
              {sourceToken.symbol}
            </div>
          </Button>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div></div>
          <div className="bl-flex bl-gap-2">
            <span>
              {balance ? balance.formatted : '--'} {sourceToken.symbol}
            </span>
            <button className="bl-text-primary" onClick={handleClickMax}>
              {t('pages.btcfi.custody.max')}
            </button>
          </div>
        </div>
        <div className="bl-absolute -bl-bottom-8 bl-left-1/2 -bl-translate-x-1/2">
          <div className="bl-size-[42px] lg:bl-size-12 bl-rounded-full bl-bg-primary bl-text-black bl-flex bl-items-center bl-justify-center">
            <ArrowDownIcon className="bl-size-6" />
          </div>
        </div>
      </div>
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black">
        <div>{t('pages.btcfi.custody.get')}</div>
        <div className="bl-flex bl-items-center bl-justify-between bl-gap-4">
          <div className="bl-text-4xl bl-text-indicator bl-grow">
            <div className="bl-max-w-40 bl-overflow-hidden bl-text-ellipsis">
              {isAmountValid ? amount : '--'}
            </div>
          </div>
          {targetTokens.length > 1 ? (
            <Select onValueChange={(v) => setTargetTokenIndex(Number(v))}>
              <ButtonSelectTrigger buttonSize="md" className="bl-w-32 bl-px-2">
                <div className="bl-flex bl-items-center bl-gap-2">
                  <DynamicIcon icon={targetToken.icon} className="bl-size-5" />
                  {targetToken.symbol}
                </div>
              </ButtonSelectTrigger>
              <SelectContent>
                {targetTokens.map((token, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    <div className="bl-flex bl-items-center bl-gap-2">
                      <DynamicIcon icon={token.icon} className="bl-size-5" />
                      {token.symbol}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Button variant="outline-3" className="bl-w-32">
              <div className="bl-flex bl-items-center bl-gap-2">
                <DynamicIcon icon={targetToken.icon} className="bl-size-5" />
                {targetToken.symbol}
              </div>
            </Button>
          )}
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div></div>
          <div className="bl-flex bl-gap-2">
            <span>
              {targetBalance ? targetBalance.formatted : '--'} {targetToken.symbol}
            </span>
          </div>
        </div>
      </div>
      <div className="bl-pt-4 lg:bl-pt-5">{renderActionButton()}</div>
    </div>
  );
}
RedeemForm.displayName = 'RedeemForm';
