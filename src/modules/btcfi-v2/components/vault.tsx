import { NavLink } from '@/components/i18n/link';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { CutCornerCard } from '@/components/ui/card';
import { DynamicIcon } from '@/components/ui/icon';
import { toPercent } from '@/lib/utils';
import { BtcfiV2Project, BtcfiV2Vault } from '@/modules/btcfi/types';
import { chainMap } from '@/wallets/config/chains';
import { ChevronDownIcon, LoaderIcon } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

type VaultsCardProps = {
  vaults: BtcfiV2Vault[];
};

export function VaultsCard({ vaults }: VaultsCardProps) {
  const { t } = useTranslation();
  return (
    <CutCornerCard
      containerClassName="bl-w-full"
      className="bl-w-full bl-min-h-40 bl-p-2 bl-pb-10 bl-bg-black bl-space-y-4"
    >
      <div className="bl-w-full bl-h-10 bl-bg-white/5 bl-corner-cutout bl-corner-cutout-tl-3 bl-corner-cutout-br-0">
        <div className="bl-w-full bl-h-full bl-flex bl-items-center bl-text-xs lg:bl-text-base lg:bl-pl-20 lg:bl-pr-10">
          <div className="bl-w-[36%] bl-pl-2 lg:bl-pl-0">{t('pages.btcfi.vault')}</div>
          <div className="bl-w-[26%]">{t('pages.btcfi.tvl')}</div>
          <div className="bl-w-[38%]">{t('pages.btcfi.estAPY')}</div>
        </div>
      </div>
      <div className="bl-space-y-5 lg:bl-px-10 lg:bl-space-y-10">
        {vaults.map((vault) => (
          <VaultItem key={vault.symbol} vault={vault} />
        ))}
      </div>
    </CutCornerCard>
  );
}

type VaultItemProps = {
  vault: BtcfiV2Vault;
};

function VaultItem({ vault }: VaultItemProps) {
  return (
    <Accordion type="single" collapsible defaultValue="item-1" className="w-full">
      <AccordionItem value="item-1">
        <AccordionTrigger className="bl-h-[55px] lg:bl-h-20 bl-bg-card-border bl-corner-cutout bl-corner-cutout-tl-2 lg:bl-corner-cutout-tl-3 bl-corner-cutout-br-0 bl-group">
          <div className="bl-w-[calc(100%-2px)] bl-h-[calc(100%-2px)] bl-pl-2 lg:bl-pl-10 bl-pr-0 bl-flex bl-items-center bl-text-left bl-bg-black bl-relative bl-left-px bl-corner-cutout">
            <div className="bl-w-[36%] bl-text-sm lg:bl-text-2xl bl-text-white bl-flex bl-items-center bl-gap-1 lg:bl-gap-4">
              <DynamicIcon icon={vault.icon} className="bl-size-5 lg:bl-size-10" />
              {vault.name}
            </div>
            <div className="bl-w-[26%] bl-text-sm lg:bl-text-xl bl-text-white">
              ${Number(vault.tvl).toLocaleString('en-US')}
            </div>
            <div className="bl-w-[38%] bl-text-sm lg:bl-text-xl bl-text-white">
              {toPercent(vault.apy.min)}~{toPercent(vault.apy.max)}
            </div>
            <ChevronDownIcon className="bl-size-4 lg:bl-size-6 bl-text-white bl-absolute bl-right-3 bl-top-1/2 -bl-translate-y-1/2 lg:bl-right-8 bl-duration-200 group-data-[state=open]:bl-rotate-180" />
            <div
              className="bl-absolute bl-top-0 bl-left-0 bl-w-full bl-h-full bl-opacity-[0.13] -bl-z-10"
              style={{
                backgroundImage: `url("/images/bg-grid.svg")`,
                backgroundSize: '10px 10px',
              }}
            ></div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="bl-pt-4">
          {vault.projects.map((project) => (
            <VaultProjectItem key={project.id} project={project} />
          ))}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

type VaultProjectItemProps = {
  project: BtcfiV2Project;
};

function VaultProjectItem({ project }: VaultProjectItemProps) {
  const { t } = useTranslation();
  const [clicked, setClicked] = React.useState(false);

  return (
    <div className="bl-w-full bl-h-16 bl-pl-2 lg:bl-pl-10 bl-pr-0 bl-flex bl-items-center bl-text-xs lg:bl-text-xl">
      <div className="bl-w-[36%] bl-h-full bl-flex bl-items-center bl-gap-1 lg:bl-gap-4 bl-border-b bl-border-card-border">
        <VaultItemProjectIcon project={project} />
        <VaultItemProjectName project={project} />
      </div>
      <div className="bl-w-[26%] bl-h-full bl-border-b bl-border-card-border bl-flex bl-items-center">
        ${Number(project.tvl).toLocaleString('en-US')}
      </div>
      <div className="bl-w-[38%] bl-h-full bl-flex bl-items-center bl-justify-between bl-border-b bl-border-card-border">
        <span>{project.apy === undefined ? '--' : toPercent(project.apy)}</span>
        {project.tag === 'coming' ? (
          <Button
            variant="secondary"
            className="btn-xs bl-h-7 bl-text-[10px] lg:bl-h-10 lg:bl-w-28 lg:bl-text-lg"
            disabled
          >
            {t('pages.btcfi.comingSoon')}
          </Button>
        ) : (
          <NavLink to={project.tag === 'btc' ? `/btcfi` : `/btcfi-v2/projects/${project.id}`}>
            {({ isPending }) => (
              <Button
                overlayFrom="none"
                className="btn-xs bl-h-7 bl-text-[10px] lg:bl-h-10 lg:bl-w-28 lg:bl-text-lg"
                disabled={clicked && isPending}
                onClick={() => setClicked(true)}
              >
                {clicked && isPending ? (
                  <LoaderIcon className="bl-size-6 bl-animate-spin" />
                ) : (
                  <span>{t('pages.btcfi.subscribe')}</span>
                )}
              </Button>
            )}
          </NavLink>
        )}
      </div>
    </div>
  );
}

function VaultItemProjectIcon({ project }: VaultProjectItemProps) {
  const chain = chainMap[project.chain];

  if (project.chain.startsWith('bitlayer')) {
    return (
      <div className="bl-w-7.5 lg:bl-w-15">
        <DynamicIcon icon={project.icon} className="bl-size-5 lg:bl-size-10" />
      </div>
    );
  }

  return (
    <div className="bl-flex bl-w-7.5 bl-min-w-0 bl-shrink-0 lg:bl-w-15">
      <DynamicIcon icon={chain.icon} className="bl-size-5 lg:bl-size-10" />
      <DynamicIcon icon={project.icon} className="bl-size-5 -bl-ml-2.5 lg:-bl-ml-5 lg:bl-size-10" />
    </div>
  );
}

function VaultItemProjectName({ project }: VaultProjectItemProps) {
  const chain = chainMap[project.chain];

  if (project.chain.startsWith('bitlayer')) {
    return <div className="bl-text-white">{project.name}</div>;
  }

  return (
    <div className="bl-text-white bl-flex bl-flex-col lg:bl-flex-row lg:bl-items-center lg:bl-gap-1">
      {chain.symbol}
      <div className="bl-size-1 bl-rounded-full bl-bg-white/30 bl-hidden lg:bl-block"></div>
      <div className="bl-opacity-30">{project.name}</div>
    </div>
  );
}
